package com.bm.atool.autojs;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import org.json.JSONObject;

import com.bm.atool.BuildConfig;


/**
 * 简化的JavaScript执行引擎
 * 使用WebView作为JS运行环境，提供基本的自动化功能
 */
public class SimpleJsEngine {
    private static final String TAG = "SimpleJsEngine";

    private Context context;
    private WebView webView;
    private Handler mainHandler;
    private JsExecutionCallback callback;
    private boolean webViewReady = false;

    public interface JsExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }

    public SimpleJsEngine(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        Log.d(TAG, "SimpleJsEngine created, initializing WebView...");
        initWebView();
    }

    private void initWebView() {
        mainHandler.post(() -> {
            try {
                Log.d(TAG, "Creating WebView instance...");
                webView = new WebView(context);
                Log.d(TAG, "WebView instance created successfully");
                Log.d(TAG, "WebView settings: " + webView.getSettings().toString());
                WebSettings settings = webView.getSettings();
                settings.setJavaScriptEnabled(true);
                settings.setDomStorageEnabled(true);
                settings.setAllowFileAccess(true);
                settings.setAllowContentAccess(true);
                settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

                // 添加额外的安全和稳定性设置
                settings.setAllowFileAccessFromFileURLs(true);  // 允许从文件URL访问文件
                settings.setAllowUniversalAccessFromFileURLs(true);  // 允许从文件URL进行通用访问
                settings.setJavaScriptCanOpenWindowsAutomatically(false);
                settings.setMediaPlaybackRequiresUserGesture(false);

                // 设置缓存模式以提高稳定性
                settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
                settings.setDatabaseEnabled(true);
                settings.setUseWideViewPort(true);
                settings.setLoadWithOverviewMode(true);
                
                // 启用调试模式（在debug版本中）
                if (BuildConfig.DEBUG) {
                    WebView.setWebContentsDebuggingEnabled(true);
                }

                // 添加JavaScript接口
                webView.addJavascriptInterface(new JsInterface(), "Android");
                Log.d(TAG, "JavaScript interface added successfully");

                // 设置WebViewClient来监听页面加载完成
                webView.setWebViewClient(new WebViewClient() {
                    @Override
                    public void onPageFinished(WebView view, String url) {
                        super.onPageFinished(view, url);
                        Log.d(TAG, "WebView page finished loading: " + url);
                        webViewReady = true;
                        Log.d(TAG, "WebView page loaded and ready");
                    }
                    
                    @Override
                    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                        super.onReceivedError(view, errorCode, description, failingUrl);
                        Log.e(TAG, "WebView error: " + errorCode + " - " + description + " at " + failingUrl);
                        // 即使出现错误，我们也标记WebView为就绪状态，以便脚本可以执行
                        webViewReady = true;
                    }
                    
                    @Override
                    public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                        super.onReceivedError(view, request, error);
                        Log.e(TAG, "WebView error: " + error.getDescription() + " (Code: " + error.getErrorCode() + ")");
                        // 即使出现错误，我们也标记WebView为就绪状态，以便脚本可以执行
                        webViewReady = true;
                    }
                    
                    @Override
                    public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                        super.onPageStarted(view, url, favicon);
                        Log.d(TAG, "WebView page started loading: " + url);
                    }
                });

                // 设置WebChromeClient来捕获JavaScript控制台消息
                webView.setWebChromeClient(new WebChromeClient() {
                    @Override
                    public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                        String message = String.format("JS %s: %s (Source: %s, Line: %d)",
                                consoleMessage.messageLevel(),
                                consoleMessage.message(),
                                consoleMessage.sourceId(),
                                consoleMessage.lineNumber());
                        
                        // 使用字符串比较而不是枚举比较，以避免兼容性问题
                        String level = consoleMessage.messageLevel().name();
                        if ("ERROR".equals(level)) {
                            Log.e(TAG, "[CONSOLE ERROR] " + message);
                        } else if ("WARNING".equals(level)) {
                            Log.w(TAG, "[CONSOLE WARNING] " + message);
                        } else if ("INFO".equals(level)) {
                            Log.i(TAG, "[CONSOLE INFO] " + message);
                        } else if ("LOG".equals(level)) {
                            Log.d(TAG, "[CONSOLE LOG] " + message);
                        } else if ("DEBUG".equals(level)) {
                            Log.d(TAG, "[CONSOLE DEBUG] " + message);
                        } else {
                            Log.d(TAG, "[CONSOLE] " + message);
                        }
                        return true;
                    }
                    
                    @Override
                    public void onProgressChanged(WebView view, int newProgress) {
                        super.onProgressChanged(view, newProgress);
                        Log.d(TAG, "WebView loading progress: " + newProgress + "%");
                    }
                    
                    @Override
                    public void onReceivedTitle(WebView view, String title) {
                        super.onReceivedTitle(view, title);
                        Log.d(TAG, "WebView received title: " + title);
                    }
                });

                // 加载基础HTML - 使用StringBuilder避免字符串连接问题
                StringBuilder htmlBuilder = new StringBuilder();
                htmlBuilder.append("<!DOCTYPE html>");
                htmlBuilder.append("<html>");
                htmlBuilder.append("<head>");
                htmlBuilder.append("<meta charset='utf-8'>");
                htmlBuilder.append("<title>SimpleAutoJs Engine</title>");
                htmlBuilder.append("</head>");
                htmlBuilder.append("<body>");
                htmlBuilder.append("<script>");
                // 添加更详细的API初始化日志
                htmlBuilder.append("console.log('[HTML] Starting AutoJs API initialization');");
                
                // 保存原始console方法并重写
                htmlBuilder.append("window.originalConsole = { log: console.log, error: console.error, warn: console.warn, info: console.info };");
                htmlBuilder.append("console.log = function(msg) { try { Android.log('[JS] ' + String(msg)); } catch(e) { window.originalConsole.error('[HTML] Log error: ' + e); } };");
                htmlBuilder.append("console.warn = function(msg) { try { Android.log('[JS WARN] ' + String(msg)); } catch(e) { window.originalConsole.error('[HTML] Warn error: ' + e); } };");
                htmlBuilder.append("console.error = function(msg) { try { Android.log('[JS ERROR] ' + String(msg)); } catch(e) { window.originalConsole.error('[HTML] Error log error: ' + e); } };");
                htmlBuilder.append("console.info = function(msg) { try { Android.log('[JS INFO] ' + String(msg)); } catch(e) { window.originalConsole.error('[HTML] Info log error: ' + e); } };");
                
                // 添加Toast功能
                htmlBuilder.append("window.toast = function(msg) { try { Android.toast(String(msg)); } catch(e) { console.error('[HTML] Toast error: ' + e); } };");
                htmlBuilder.append("var toast = window.toast;");
                
                // 添加Sleep功能（注意：阻塞式sleep可能会导致WebView无响应，建议使用setTimeout替代）
                htmlBuilder.append("window.sleep = function(ms) { var start = Date.now(); while (Date.now() - start < ms) { } };");
                htmlBuilder.append("var sleep = window.sleep;");
                
                // 添加设备信息
                htmlBuilder.append("window.device = { width: window.screen ? window.screen.width : 1080, height: window.screen ? window.screen.height : 1920, brand: 'Android', model: 'Unknown' };");
                htmlBuilder.append("var device = window.device;");
                
                // 添加Auto.js兼容对象
                htmlBuilder.append("window.auto = { service: false };");
                htmlBuilder.append("var auto = window.auto;");
                
                // 添加获取当前包名功能
                htmlBuilder.append("window.currentPackage = function() { try { return Android.getCurrentPackage(); } catch(e) { console.error('[HTML] CurrentPackage error: ' + e); return 'unknown'; } };");
                htmlBuilder.append("var currentPackage = window.currentPackage;");
                
                // 简化的全局错误处理器
                htmlBuilder.append("window.onerror = function(message, source, lineno, colno, error) { " +
                        "try { " +
                        "  var errorInfo = 'Global error: ' + (message || 'Unknown') + ' at ' + (source || 'unknown') + ':' + (lineno || 0) + ':' + (colno || 0); " +
                        "  if (error && error.stack) errorInfo += '\\nStack: ' + error.stack; " +
                        "  Android.log('[JS ERROR] ' + errorInfo); " +
                        "  Android.onScriptError('Global', errorInfo); " +
                        "} catch(e) { " +
                        "  Android.log('[JS ERROR] Error handler failed: ' + e); " +
                        "} " +
                        "return true; " +
                        "};");
                
                // 添加未处理的Promise拒绝处理
                htmlBuilder.append("window.addEventListener('unhandledrejection', function(event) { " +
                        "var reason = event.reason || 'Unknown promise rejection'; " +
                        "var detailedReason = '[PROMISE] '; " +
                        "if (reason && typeof reason === 'object') { " +
                        "  detailedReason += reason.toString ? reason.toString() : 'Unknown promise rejection'; " +
                        "  if (reason.stack) detailedReason += '\\nStack: ' + reason.stack; " +
                        "  if (reason.message) detailedReason = (reason.name || 'Error') + ': ' + detailedReason + ': ' + reason.message; " +
                        "} else { " +
                        "  detailedReason += String(reason); " +
                        "} " +
                        "console.error('[HTML] Unhandled promise rejection: ' + detailedReason); " +
                        "try { Android.onScriptError('Promise', detailedReason); } catch(e) { " +
                        "  console.error('[HTML] Promise error handler failed: ' + e); " +
                        "} " +
                        "event.preventDefault(); " +
                        "});");
                
                // 标记API已就绪
                htmlBuilder.append("window.autoJsReady = true;");
                htmlBuilder.append("console.log('[HTML] AutoJs API initialized successfully');");
                htmlBuilder.append("console.log('[HTML] Available globals: ' + Object.keys(window).filter(function(k) { return k !== 'webkit' && k !== 'chrome'; }).join(', '));");
                htmlBuilder.append("</script>");
                htmlBuilder.append("</body>");
                htmlBuilder.append("</html>");
                String html = htmlBuilder.toString();
                
                // Validate HTML structure
                if (!html.contains("<script>") || !html.contains("</script>")) {
                    Log.e(TAG, "HTML validation failed: Missing script tags");
                }
                if (!html.contains("window.autoJsReady = true;")) {
                    Log.e(TAG, "HTML validation failed: Missing autoJsReady flag");
                }
                
                Log.d(TAG, "Loading HTML into WebView, HTML length: " + html.length());
                // 只在DEBUG模式下记录完整的HTML内容，避免日志过大
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "HTML content: " + html);
                }
                webView.loadDataWithBaseURL("about:blank", html, "text/html", "utf-8", null);
                
                Log.d(TAG, "WebView initialized successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error initializing WebView", e);
                // 即使初始化失败，我们也标记WebView为就绪状态，以便脚本可以执行
                webViewReady = true;
            }
        });
    }

    /**
     * 执行JavaScript脚本
     */
    public void executeScript(String scriptContent, String scriptName, JsExecutionCallback callback) {
        Log.d(TAG, "executeScript called: " + scriptName + ", content length: " + scriptContent.length());
        this.callback = callback;

        if (callback != null) {
            callback.onStart(scriptName);
        }

        // 等待WebView准备就绪后再执行脚本
        waitForWebViewReady(() -> executeScriptInternal(scriptContent, scriptName));
    }

    private void waitForWebViewReady(Runnable onReady) {
        waitForWebViewReady(onReady, 0);
    }
    
    private void waitForWebViewReady(Runnable onReady, int retryCount) {
        mainHandler.post(() -> {
            if (webView == null) {
                Log.e(TAG, "WebView is null when waiting for ready state");
                if (callback != null) {
                    callback.onError("Script", "WebView not initialized");
                }
                return;
            }
            
            if (webViewReady) {
                Log.d(TAG, "WebView is ready, executing script");
                onReady.run();
            } else {
                // 增加重试次数限制，避免无限等待
                if (retryCount > 100) { // 最多等待10秒 (100 * 100ms)
                    Log.e(TAG, "WebView not ready after maximum retries (100 attempts, 10 seconds timeout)");
                    if (callback != null) {
                        callback.onError("Script", "WebView not ready after maximum retries (10 seconds timeout)");
                    }
                    return;
                }
                
                Log.d(TAG, "WebView not ready yet, waiting... (retry: " + retryCount + ")");
                // 如果WebView还没准备好，等待一段时间后重试
                mainHandler.postDelayed(() -> waitForWebViewReady(onReady, retryCount + 1), 100);
            }
        });
    }

    private void executeScriptInternal(String scriptContent, String scriptName) {
        Log.d(TAG, "executeScriptInternal called for: " + scriptName);
        mainHandler.post(() -> {
            try {
                Log.d(TAG, "About to execute script in WebView");
                Log.d(TAG, "Script content length: " + scriptContent.length());
                
                // 在DEBUG模式下记录脚本内容的前200个字符，避免日志过大
                if (BuildConfig.DEBUG) {
                    String preview = scriptContent.length() > 200 ? scriptContent.substring(0, 200) + "..." : scriptContent;
                    Log.d(TAG, "Script content preview: " + preview);
                }

                // 包装脚本以捕获错误和结果，确保API已加载
                // 使用 JSONObject.quote 安全地转义用户脚本，避免字符串拼接导致的语法错误
                final String scriptJson = JSONObject.quote(scriptContent);
                final String scriptNameJson = JSONObject.quote(scriptName);
                Log.d(TAG, "Script JSON prepared, length: " + scriptJson.length());

                // 修复return语句问题：使用Function构造器包装用户代码
                String wrappedScript =
                    "try {" +
                    "  console.log('[JS] Starting script execution');" +
                    "  if (!window.autoJsReady) {" +
                    "    Android.onScriptError(" + scriptNameJson + ", 'API not ready');" +
                    "  } else {" +
                    "    console.log('[JS] API ready, executing script');" +
                    "    var userCode = " + scriptJson + ";" +
                    "    console.log('[JS] User code loaded, length: ' + userCode.length);" +
                    "    // 使用Function构造器创建函数，这样return语句就是合法的" +
                    "    var scriptFunction = new Function(userCode);" +
                    "    var result = scriptFunction();" +
                    "    console.log('[JS] Script completed with result: ' + result);" +
                    "    Android.onScriptSuccess(" + scriptNameJson + ", String(result || 'completed'));" +
                    "  }" +
                    "} catch (e) {" +
                    "  console.error('[JS] Error: ' + e.message);" +
                    "  console.error('[JS] Error stack: ' + e.stack);" +
                    "  Android.onScriptError(" + scriptNameJson + ", 'Error: ' + e.message + (e.stack ? '\\nStack: ' + e.stack : ''));" +
                    "}";

                Log.d(TAG, "About to call webView.evaluateJavascript");
                // 添加执行前的日志记录
                Log.d(TAG, "Executing script with name: " + scriptName);
                Log.d(TAG, "Wrapped script length: " + wrappedScript.length());
                
                // 在DEBUG模式下记录包装脚本的前500个字符
                if (BuildConfig.DEBUG) {
                    String wrappedPreview = wrappedScript.length() > 500 ? wrappedScript.substring(0, 500) + "..." : wrappedScript;
                    Log.d(TAG, "Wrapped script preview: " + wrappedPreview);
                }
                
                webView.evaluateJavascript(wrappedScript, result -> {
                    // 添加执行后的日志记录
                    Log.d(TAG, "webView.evaluateJavascript completed for: " + scriptName);
                    if (result != null) {
                        Log.d(TAG, "JavaScript evaluation result: " + result);
                    } else {
                        Log.d(TAG, "JavaScript evaluation result is null");
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error executing script: " + scriptName, e);
                if (callback != null) {
                    callback.onError(scriptName, e.getMessage() + "\nStack: " + Log.getStackTraceString(e));
                }
            }
        });
    }

    /**
     * 停止脚本执行
     */
    public void stopScript() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.evaluateJavascript("window.stop();", null);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error stopping script", e);
            }
        });
    }

    /**
     * 释放资源
     */
    public void release() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.destroy();
                    webView = null;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error releasing WebView", e);
            }
        });
    }

    /**
     * JavaScript接口类
     */
    private class JsInterface {
        
        @JavascriptInterface
        public void log(String message) {
            try {
                Log.d(TAG, "JS Log: " + message);
            } catch (Exception e) {
                Log.e(TAG, "Error in log interface", e);
            }
        }
        
        @JavascriptInterface
        public void toast(String message) {
            mainHandler.post(() -> {
                try {
                    if (context != null) {
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error showing toast", e);
                }
            });
        }
        
        @JavascriptInterface
        public String getCurrentPackage() {
            try {
                // 这里可以返回当前应用的包名
                if (context != null) {
                    return context.getPackageName();
                }
                return "unknown";
            } catch (Exception e) {
                Log.e(TAG, "Error getting current package", e);
                return "unknown";
            }
        }
        
        @JavascriptInterface
        public void onScriptSuccess(String scriptName, String result) {
            mainHandler.post(() -> {
                try {
                    Log.d(TAG, "onScriptSuccess called for: " + scriptName + ", result: " + result);
                    if (callback != null) {
                        callback.onSuccess(scriptName, result);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in onScriptSuccess callback", e);
                }
            });
        }
        
        @JavascriptInterface
        public void onScriptError(String scriptName, String error) {
            mainHandler.post(() -> {
                try {
                    Log.e(TAG, "Script error reported from JS: " + scriptName);
                    // 分行记录错误信息，使其更易读
                    String[] errorLines = error.split("\n");
                    for (int i = 0; i < errorLines.length; i++) {
                        if (i == 0) {
                            Log.e(TAG, "Error message: " + errorLines[i]);
                        } else {
                            Log.e(TAG, "Error stack [" + i + "]: " + errorLines[i]);
                        }
                    }
                    if (callback != null) {
                        callback.onError(scriptName, error);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in onScriptError callback", e);
                }
            });
        }
    }
}